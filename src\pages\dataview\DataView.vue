<template>
  <div class="data-view-wrapper">
    <!-- 权限不足提示 -->
    <div v-if="!hasDataViewPermission()" class="permission-denied">
      <a-result
        status="403"
        title="权限不足"
        sub-title="抱歉，您没有权限访问数据视图页面"
      >
        <template #extra>
          <a-button type="primary" @click="$router.go(-1)">返回</a-button>
        </template>
      </a-result>
    </div>

    <!-- 项目选择区域 -->
    <div v-else-if="!showWorkspace" class="project-selection-area">
      <div
        v-for="group in projectGroups"
        :key="group.key"
        class="project-group"
      >
        <!-- 分组标题行 -->
        <a-row
          type="flex"
          justify="space-between"
          align="middle"
          class="group-header"
        >
          <a-col>
            <h3 class="group-title">{{ group.title }}</h3>
          </a-col>
          <a-col v-if="group.children && group.children.length > 4 && checkGroupPermission(group)">
            <a-button
              type="link"
              @click="toggleGroupExpansion(group)"
              class="jump-button"
            >
              {{ expandedGroups.has(group.key) ? '收起' : '查看全部' }}
              <a-icon :type="expandedGroups.has(group.key) ? 'up' : 'down'" />
            </a-button>
          </a-col>
        </a-row>

        <!-- 项目卡片行 -->
        <a-row :gutter="[16, 16]" class="project-cards">
          <a-col
            v-for="project in getDisplayProjects(group)"
            :key="project.key"
            :span="responsiveSpan"
          >
            <a-card
              hoverable
              :class="[
                'project-card',
                {
                  disabled: !checkProjectPermission(project)
                }
              ]"
            >
              <div class="card-content">
                <!-- <div class="project-name">{{ project.title }}</div> -->
                <div class="project-actions">
                  <a-button
                    type="primary"
                    size="small"
                    :disabled="!checkProjectPermission(project)"
                    @click="handleProjectJump(project)"
                  >
                    {{ project.label }}
                  </a-button>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 工作台内容 -->
    <div class="workspace-content" v-else-if="showWorkspace && hasDataViewPermission()">
      <div class="workspace-header">
        <h2 class="workspace-title">{{ (selectedProjectData && selectedProjectData.title) || '工作台' }}</h2>
        <a-button
          type="link"
          @click="handleBackToParent"
          class="back-button"
        >
          <a-icon type="arrow-left" /> 返回项目选择
        </a-button>
      </div>
      <Index
        :selected-project="selectedProject"
        :selected-project-data="selectedProjectData"
        :selected-data-source="selectedDataSource"
        @data-source-change="handleDataSourceChange"
        @back-to-parent="handleBackToParent"
      />
    </div>
  </div>
</template>

<script>
import Index from './index.vue';
import { Http } from '@/utils/request';

export default {
  name: 'DataView',
  components: {
    Index
  },
  data() {
    // 获取用户角色信息，用于权限控制
    let role = this.$store.state.account.user.role;
    return {
      role,
      projectGroups: [],
      selectedProject: null,
      selectedProjectData: null, // 存储选中项目的完整数据
      selectedDataSource: null,
      loading: false,
      showWorkspace: false, // 控制是否显示工作台
      expandedGroups: new Set() // 记录展开显示全部卡片的分组
    };
  },
  computed: {
    // 响应式计算span值
    responsiveSpan() {
      // 这里可以根据实际需求调整断点
      if (typeof window !== 'undefined') {
        const width = window.innerWidth;
        if (width <= 576) return 24; // 小屏幕：1个一行
        if (width <= 768) return 12; // 中屏幕：2个一行
        if (width <= 1200) return 8; // 大屏幕：3个一行
        return 6; // 超大屏幕：4个一行
      }
      return 6; // 默认值
    }
  },
  mounted() {
    // 监听窗口大小变化以触发响应式重新计算
    this.handleResize = () => {
      this.$forceUpdate(); // 强制更新以重新计算responsiveSpan
    };
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 清理事件监听器
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }
  },
  created() {
    // 检查用户是否有数据查看权限
    if (!this.hasDataViewPermission()) {
      this.$message.error('您没有权限访问数据视图页面');
      // 可以选择跳转到其他页面或显示权限不足页面
      // this.$router.push({ path: '/exception/403' });
      return;
    }

    this.loadProjectData();
  },
  methods: {
    // 获取项目参数配置（复用Todo组件的逻辑）
    getProjectParams() {
      return {
        url: '/sqlreview/report/report_all_project_group',
        reqParams: {},
        placeholder: '请选择项目或项目组',
        beforeLoaded: res => {
          const arr = res.map(item => {
            return {
              ...item,
              title: item.label,
              key: item.value,
              children:
                item.children &&
                item.children.map(el => {
                  return {
                    ...el,
                    title: el.label,
                    key: el.value
                  };
                })
            };
          });
          return arr;
        }
      };
    },

    // 加载项目数据
    async loadProjectData() {
      this.loading = true;
      try {
        // const params = this.getProjectParams();
        // const response = await Http({
        //   url: params.url,
        //   method: 'get',
        //   params: params.reqParams
        // });

        // if (response.data && response.data.code === 0) {
        //   const processedData = params.beforeLoaded(response.data.data || []);
        //   this.projectGroups = this.processProjectData(processedData);
        // }

        // 模拟数据 - 符合 [{value: '', label: '', children: [{value: '', label: ''}]}] 格式
        const mockData = [
          {
            value: 'project_group_1',
            label: '核心业务系统',
            children: [
              {
                value: 'project_1_1',
                label: '用户管理系统'
              },
              {
                value: 'project_1_2',
                label: '订单管理系统'
              },
              {
                value: 'project_1_3',
                label: '支付管理系统'
              },
              {
                value: 'project_1_4',
                label: '库存管理系统'
              },
              {
                value: 'project_1_5',
                label: '供应链管理系统'
              },
              {
                value: 'project_1_6',
                label: '物流管理系统'
              },
              {
                value: 'project_1_7',
                label: '客服管理系统'
              },
              {
                value: 'project_1_8',
                label: '营销管理系统'
              }
            ]
          },
          {
            value: 'project_group_2',
            label: '数据分析平台',
            children: [
              {
                value: 'project_2_1',
                label: '实时数据监控'
              },
              {
                value: 'project_2_2',
                label: '业务报表系统'
              }
            ]
          },
          {
            value: 'project_group_3',
            label: '运维工具',
            children: [
              {
                value: 'project_3_1',
                label: '日志分析系统'
              },
              {
                value: 'project_3_2',
                label: '性能监控平台'
              },
              {
                value: 'project_3_3',
                label: '自动化部署工具'
              }
            ]
          },
          {
            value: 'standalone_project_1',
            label: '独立项目 - 文档管理系统',
            children: []
          },
          {
            value: 'standalone_project_2',
            label: '独立项目 - 消息通知服务',
            children: []
          }
        ];

        // 使用 beforeLoaded 方法处理数据
        const params = this.getProjectParams();
        const processedData = params.beforeLoaded(mockData);
        this.projectGroups = this.processProjectData(processedData);
      } catch (error) {
        console.error('加载项目数据失败:', error);
        this.$message.error('加载项目数据失败');
      } finally {
        this.loading = false;
      }
    },

    // 处理项目数据，按是否有子项目分组
    processProjectData(data) {
      const groups = [];

      // 根据权限过滤项目数据
      const filteredData = this.filterProjectsByPermission(data);

      // 有子项目的分组
      const withChildren = filteredData.filter(
        item => item.children && item.children.length > 0
      );
      withChildren.forEach(item => {
        // 对子项目也进行权限过滤
        const filteredChildren = this.filterProjectsByPermission(item.children);
        if (filteredChildren.length > 0) {
          groups.push({
            key: item.key,
            title: item.title,
            children: filteredChildren,
            hasChildren: true
          });
        }
      });

      // 没有子项目的，归为"其它"分组
      const withoutChildren = filteredData.filter(
        item => !item.children || item.children.length === 0
      );
      if (withoutChildren.length > 0) {
        groups.push({
          key: 'others',
          title: '其它',
          children: withoutChildren,
          hasChildren: false
        });
      }

      return groups;
    },

    // 根据用户权限过滤项目列表
    filterProjectsByPermission(projects) {
      if (!projects || !Array.isArray(projects)) {
        return [];
      }

      return projects.filter(project => {
        // 使用权限系统检查项目访问权限
        // 如果项目有特定的权限码，使用$permission检查
        if (project.permission_code) {
          return this.$permission.project(project.permission_code);
        }

        // 如果项目有角色限制，检查用户角色
        if (project.visible_roles && Array.isArray(project.visible_roles)) {
          return project.visible_roles.includes(this.role);
        }

        // 默认情况下，根据用户角色进行基本权限控制
        // developer 和 leader 角色可能有不同的项目访问权限
        if (['developer', 'leader'].includes(this.role)) {
          // 开发者和组长可能只能看到自己相关的项目
          // 这里可以根据具体业务需求调整
          return true; // 暂时允许访问所有项目
        }

        // 管理员和DBA默认可以访问所有项目
        return true;
      });
    },

    // 获取要显示的项目（根据展开状态决定显示数量）
    getDisplayProjects(group) {
      if (!group.children) return [];
      // 如果分组已展开，显示全部项目；否则最多显示4个
      return this.expandedGroups.has(group.key) ? group.children : group.children.slice(0, 4);
    },

    // 处理项目跳转（进入工作台）
    handleProjectJump(project) {
      // 检查项目访问权限
      if (!this.checkProjectPermission(project)) {
        this.$message.warning('您没有权限访问该项目');
        return;
      }

      // 设置选中的项目数据并进入工作台
      this.selectedProject = project.value; // 使用project.value作为treeId
      this.selectedProjectData = project; // 保存完整的项目数据
      this.selectedDataSource = null; // 重置数据源选择
      this.showWorkspace = true; // 显示工作台

      // 触发数据刷新事件
      this.$emit('project-change', project.value);
    },

    // 切换分组展开/收起状态
    toggleGroupExpansion(group) {
      // 检查分组访问权限
      if (!this.checkGroupPermission(group)) {
        this.$message.warning('您没有权限访问该项目组');
        return;
      }

      // 切换展开状态
      if (this.expandedGroups.has(group.key)) {
        this.expandedGroups.delete(group.key);
      } else {
        this.expandedGroups.add(group.key);
      }

      // 触发响应式更新
      this.$forceUpdate();
    },

    // 检查项目权限
    checkProjectPermission(project) {
      // 使用权限系统检查项目访问权限
      if (project.permission_code) {
        return this.$permission.project(project.permission_code);
      }

      // 如果项目有角色限制，检查用户角色
      if (project.visible_roles && Array.isArray(project.visible_roles)) {
        return project.visible_roles.includes(this.role);
      }

      // 默认权限检查
      return true;
    },

    // 检查项目组权限
    checkGroupPermission(group) {
      // 使用权限系统检查项目组访问权限
      if (group.permission_code) {
        return this.$permission.projectGroup(group.permission_code);
      }

      // 如果项目组有角色限制，检查用户角色
      if (group.visible_roles && Array.isArray(group.visible_roles)) {
        return group.visible_roles.includes(this.role);
      }

      // 默认权限检查
      return true;
    },

    // 处理数据源变化
    handleDataSourceChange(dataSource) {
      this.selectedDataSource = dataSource;
    },

    // 处理返回父级卡片
    handleBackToParent() {
      this.selectedProject = null;
      this.selectedProjectData = null;
      this.selectedDataSource = null;
      this.showWorkspace = false; // 隐藏工作台，回到项目选择页面
    },

    // 显示权限不足提示
    showPermissionDeniedMessage(type = 'project') {
      const messages = {
        project: '您没有权限访问该项目',
        group: '您没有权限访问该项目组',
        data: '您没有权限查看项目数据'
      };
      this.$message.warning(messages[type] || '权限不足');
    },

    // 检查用户是否有数据查看权限
    hasDataViewPermission() {
      // 调试信息：打印当前权限状态
      // console.log('当前用户角色:', this.role);
      // console.log('权限页面列表:', this.$store.state.auth.pages);

      // 临时禁用权限检查，直接返回 true
      return true;

      // // 检查页面级权限
      // const auth = this.$store.state.auth;
      // if (auth && auth.pages) {
      //   const hasPagePermission = auth.pages.some(page =>
      //     page.id.includes('data-view') || page.id.includes('dataview')
      //   );
      //   if (!hasPagePermission) {
      //     return false;
      //   }
      // }

      // // 检查角色权限
      // const allowedRoles = ['admin', 'dba', 'leader', 'developer'];
      // return allowedRoles.includes(this.role);
    }
  }
};
</script>

<style lang="less" scoped>
.data-view-wrapper {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.permission-denied {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  background: #fff;
  border-radius: 8px;
  margin: 24px 0;
}

.project-selection-area {
  margin-bottom: 24px;
}

.project-group {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.group-header {
  margin-bottom: 16px;
  padding: 0 8px;
}

.group-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.jump-button {
  padding: 0;
  font-size: 14px;

  &:hover {
    color: #1890ff;
  }
}

.project-cards {
  .ant-col {
    display: flex;
  }
}

.project-card {
  width: 100%;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover:not(.disabled) {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  &.active {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: #f5f5f5;

    .project-name {
      color: #bfbfbf;
    }

    &:hover {
      box-shadow: none;
      transform: none;
    }
  }

  .ant-card-body {
    padding: 16px;
  }
}

.card-content {
  display: flex;
  // flex-direction: column;
  align-items: center;
  text-align: center;
  min-height: 80px;
  justify-content: space-between;
}

.project-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 12px;
  line-height: 1.4;
  word-break: break-word;
}

.project-actions {
  width: 100%;
  height: 100%;
  display: flex;

  .ant-btn {
    width: 100%;
  }
}

.workspace-content {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.workspace-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 5px solid #f0f0f0;
  margin-bottom: 40px;
  background: #fafafa;

  .back-button {
    padding: 0;
    font-size: 14px;
    color: #1890ff;

    &:hover {
      color: #40a9ff;
    }
  }

  .workspace-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #262626;
  }
}

// 响应式设计 - span值通过JavaScript计算属性动态控制
@media (max-width: 768px) {
  .data-view-wrapper {
    padding: 16px;
  }

  .group-title {
    font-size: 16px;
  }
}

@media (max-width: 576px) {
  .data-view-wrapper {
    padding: 12px;
  }

  .project-group {
    margin-bottom: 24px;
  }
}
</style>
